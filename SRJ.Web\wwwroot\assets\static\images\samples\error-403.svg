<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 1104 917" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M907,539.056C907,534.085 902.971,530.056 898,530.056C893.029,530.056 889,534.085 889,539.056L889,1139.06C889,1144.03 893.029,1148.06 898,1148.06C902.971,1148.06 907,1144.03 907,1139.06L907,539.056Z" style="fill:rgb(43,37,78);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M552,567.055L544.5,602.055L498.5,614.055L417,637.055L445,602.055L399.5,611.055L439.5,587.629L404.5,587.555L368,564.055L411.5,567.129C452.167,572.08 472.5,574.555 472.5,574.555C472.5,574.555 452.167,562.055 411.5,537.055L552,567.055ZM1286,594.055L1426.5,564.055C1404.83,579.104 1394,586.629 1394,586.629C1394,586.629 1406.67,589.104 1432,594.055L1472,621.055L1432,610.129L1380.5,610.055L1438.5,638.055L1374.5,621.055L1421,664.055L1346.5,629.055L1293.5,629.055L1286,594.055ZM911.016,311.035L909.828,283.007L898.83,261.449L927.742,281.785L926.152,256.096L893.106,232L906.331,234.523L933.406,250.653L941.53,276.845L948.676,237.498L954.877,269.401L971.061,247.485L960.293,288.931L981.5,264.555L963,315.055L911.016,311.035ZM783.338,896.872L809.716,914.454L803.906,961.291L795.051,1044.8L786.911,1032.84L783.411,1001.84L749.911,1044.84L764.248,990.488L742.911,1032.84L723.911,1055.84L730.411,1027.34C743.411,994.677 749.911,978.343 749.911,978.343C749.911,978.343 743.411,989.01 730.411,1010.34L710.476,1019.87L783.338,896.872ZM918.348,918.881L949.013,926.917L958.768,973.094L977.583,1054.94L965.5,1044.63L953.74,1017.24L952.592,1018.11L952.5,1076.13L946,1063.63L934.5,988.129L933,988.129L929,1044.63L922,1080.13L920,1080.13L915.5,1044.63L915.5,1013.63L907,1038.13L889.5,1058.9L918.348,918.881Z" style="fill:rgb(255,210,91);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M898,529.629L845.5,664.129L973,664.129L935.5,539.629L898,529.629Z" style="fill:rgb(255,210,91);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M779.5,835.056L758.072,891.097C751.841,907.39 750.769,925.206 755,942.129C758.667,947.462 766.833,951.129 779.5,953.129C792.167,955.129 801.352,955.18 807.055,953.283L814.019,945.737C822.919,936.093 829.834,924.791 834.369,912.476L890.217,760.823L898,760.629L898,905.797C898,922.344 902.704,938.549 911.564,952.524C918.855,954.927 927.333,956.129 937,956.129C946.667,956.129 953.167,954.296 956.5,950.629C967.252,937.922 973.945,922.281 975.714,905.73L990,772.056L973,664.129C962.778,661.462 950.278,660.129 935.5,660.129C920.722,660.129 890.722,658.271 845.5,654.556L779.5,835.056Z" style="fill:rgb(255,109,91);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M848.666,874.146L813.5,881.629L850.236,844.188L839.973,782.065L877.374,804.053L848.666,874.146Z" style="fill:rgb(255,210,91);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M970,538.556C997.333,557.889 1025.17,571.556 1053.5,579.556C1081.83,587.556 1163.67,587.556 1299,579.556L1299,664.129L1124.06,670.557C1113.75,670.936 1103.44,669.717 1093.5,666.944L990,638.056L1053.5,819.056L1030.71,821.747L1018.5,810.129L1013.5,821.629L983.249,827.351L973,810.129L962.658,829.782L918,835.056L929,542.556L970,538.556ZM862.5,530.056L900.5,536.556L796.5,835.056L753,825.629L749,813.129L737.31,822.343L727,820.129L727,798.129L707.615,817.255L683,813.056L796.5,608.556C761.167,623.605 735.167,631.129 718.5,631.129L539.5,631.129L539.5,552.556C657.167,545.889 729.667,542.556 757,542.556C784.333,542.556 819.5,538.389 862.5,530.056Z" style="fill:rgb(43,37,78);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M1299,579.556L1386.75,579.556C1390.75,579.556 1394,582.802 1394,586.806C1394,590.81 1390.75,594.056 1386.75,594.056L1299,594.056L1299,579.556ZM452.25,579.556L540,579.556L540,594.056L452.25,594.056C448.246,594.056 445,590.81 445,586.806C445,582.802 448.246,579.556 452.25,579.556Z" style="fill:rgb(132,125,170);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M955.145,563.555L965.432,723.072L965.5,723.055L967,739.129L961.5,739.055L961.5,724.496L952.162,725.355L953.5,739.629L948,739.629L948,725.738L939.18,726.549L940.5,740.629L935,740.629L935,726.933L926,727.761L926,741.629L921.5,741.629L921.986,727.528L918.229,563.555L892.5,563.555C891,563.555 872,526.555 942,532.055C966.571,533.986 978.71,528.461 986,532.055C990.861,534.451 991.694,541.118 988.5,552.055C976.834,559.722 966.834,563.555 958.5,563.555L955.145,563.555Z" style="fill:rgb(96,88,142);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M751.5,433.129C756.014,452.985 769.014,468.46 790.5,479.556C811.986,490.651 844.986,499.151 889.5,505.056L1022.32,505.121L1045.36,494.945L1058.16,489.294C1072.54,482.941 1085.04,472.967 1094.42,460.346L1099,454.191C1098.18,449.65 1085.35,442.296 1060.5,432.129C1035.65,421.963 1003.29,411.157 963.414,399.713C947.42,388.908 923.711,386.319 892.286,391.946C863.976,397.015 820.392,396.975 768,418.129C764.157,419.681 758.657,424.681 751.5,433.129Z" style="fill:rgb(43,37,78);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M850.5,524.056C844.5,525.389 841.5,530.222 841.5,538.556C841.5,546.889 858.5,555.247 892.5,563.629C896.724,557.472 900.891,553.139 905,550.629C909.109,548.119 916.442,545.428 927,542.556L850.5,524.056Z" style="fill:rgb(132,125,170);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M909.798,540.562C932.039,542.899 940.981,544.423 969.952,541.018C977.453,540.137 978.968,536.004 985.501,533.128C989.663,531.296 995.001,528.628 998.501,527.628C1022.76,520.698 1026.19,490.667 1028.84,465.496C1032.63,429.415 993.588,391.93 964.501,376.128C948.145,367.243 949.794,373.006 927.653,370.679C912.348,369.07 924.313,364.406 910.501,368.128C868.795,379.367 818.001,404.128 810.001,439.128C804.146,464.74 808.501,502.628 837.501,517.628C839.225,518.52 847.01,522.021 850.501,524.128C853.838,526.143 854.385,529.266 856.001,530.128C871.339,538.321 889.388,538.417 909.798,540.562Z" style="fill:rgb(255,109,91);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M876.488,416.008L890.16,417.929C895.046,418.616 899.281,421.661 901.488,426.073C907.058,437.209 909.731,443.575 909.507,445.172C909.283,446.766 905.119,451.082 897.015,458.12C893.739,460.966 889.39,462.253 885.092,461.649L869.249,459.423C864.615,458.771 860.551,455.995 858.259,451.916C853.978,444.297 851.994,439.372 852.307,437.142C852.625,434.886 856.099,429.652 862.73,421.439C866.042,417.336 871.266,415.274 876.488,416.008ZM974.969,427.391L984.806,428.251C989.984,428.704 994.558,431.805 996.896,436.446C1002.12,446.811 1004.66,452.78 1004.52,454.354C1004.38,455.915 1000.98,460.28 994.316,467.448C991.166,470.835 986.631,472.579 982.024,472.176L970.036,471.127C965.086,470.694 960.671,467.837 958.248,463.499C954.371,456.557 952.527,452.001 952.717,449.831C952.911,447.613 955.708,442.377 961.109,434.122C964.135,429.495 969.461,426.909 974.969,427.391Z" style="fill:white;"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M886,444.629C882.134,444.629 879,441.495 879,437.629C879,433.763 882.134,430.629 886,430.629C889.866,430.629 893,433.763 893,437.629C893,441.495 889.866,444.629 886,444.629ZM980.819,455.811C976.953,455.811 973.819,452.677 973.819,448.811C973.819,444.945 976.953,441.811 980.819,441.811C984.685,441.811 987.819,444.945 987.819,448.811C987.819,452.677 984.685,455.811 980.819,455.811ZM935.634,435.538C935.908,434.877 936.514,434.413 937.223,434.321C938.319,434.178 939.322,434.951 939.464,436.046L944.67,476.091C944.691,476.253 944.692,476.416 944.674,476.578C944.547,477.675 943.555,478.462 942.458,478.336L921.634,475.933C921.45,475.912 921.269,475.865 921.097,475.793C920.077,475.37 919.593,474.2 920.016,473.18L935.634,435.538ZM846.74,476.051L870.357,476.905L887.864,490.718L908.858,482.734L924.369,496.686L948.715,486.463L961.882,495.566L974.397,488.175L1002.42,494.736L974.99,496.655L956.873,509.951L944.923,496.752L921.68,508.402L905.066,493.023L888.701,502.689L868.955,485.524L846.74,476.051Z" style="fill:rgb(43,37,78);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M896.439,312.655L893.5,345.555L971,355.055L972.496,321.024C972.791,314.312 968.276,308.337 961.739,306.787C948.107,303.555 937.396,301.498 929.606,300.617C923.395,299.914 916.832,299.678 909.918,299.909C902.851,300.144 897.069,305.612 896.439,312.655Z" style="fill:rgb(96,88,142);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M898,308.129C877.667,351.462 848.667,380.771 811,396.055C786.128,406.148 768.433,411.98 759.196,421.762C756.268,424.863 753.703,428.652 751.5,433.129C760.318,435.902 766.818,437.736 771,438.629C822.5,449.629 860.832,443.542 884,439.129C895.06,437.022 911.84,432.939 934.341,426.88C947.215,423.414 960.652,422.556 973.861,424.359C1028.92,431.871 1062.64,437.628 1075,441.629C1088.82,446.101 1096.71,450.577 1098.68,455.055C1100.16,444.697 1096.46,436.559 1087.59,430.64C1083.27,427.757 1058.04,422.068 1039.5,410.055C1029.4,403.509 1015.28,391.008 997.156,372.553C986.357,361.558 978.833,347.773 975.426,332.742L972,317.629L898,308.129Z" style="fill:rgb(96,88,142);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M852.5,371.129L884.5,350.129L898,381.629L914,350.129L944,346.129L914,332.629L893.5,311.629L852.5,371.129Z" style="fill:rgb(43,37,78);fill-rule:nonzero;"/>
        <path d="M906,325.129L884.5,376.629L898,389.129L958.5,346.629L906,325.129Z" style="fill:rgb(255,210,91);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M543,349.629C565.644,349.629 584,331.273 584,308.629C584,285.985 565.644,267.629 543,267.629C520.356,267.629 502,285.985 502,308.629C502,331.273 520.356,349.629 543,349.629Z" style="fill:rgb(235,238,246);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M633,417.629L626,439.129L601,439.129C601,439.129 609.333,441.296 626,445.629C628.667,463.296 630,472.129 630,472.129C630,472.129 631.667,464.129 635,448.129L656.5,443.129L635,439.129L633,417.629Z" style="fill:rgb(255,210,91);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M1146,308.629L1139,330.129L1114,330.129C1114,330.129 1122.33,332.296 1139,336.629C1141.67,354.296 1143,363.129 1143,363.129C1143,363.129 1144.67,355.129 1148,339.129L1169.5,334.129L1148,330.129L1146,308.629Z" style="fill:rgb(255,210,91);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M1193.22,502.629L1187.92,518.803L1169,518.803C1169,518.803 1175.31,520.433 1187.92,523.693C1189.94,536.984 1190.95,543.629 1190.95,543.629C1190.95,543.629 1192.21,537.611 1194.73,525.574L1211,521.812L1194.73,518.803L1193.22,502.629Z" style="fill:rgb(255,210,91);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M736.333,306.129L731.667,320.331L715,320.331C715,320.331 720.556,321.762 731.667,324.624C733.444,336.294 734.333,342.129 734.333,342.129C734.333,342.129 735.444,336.845 737.667,326.276L752,322.973L737.667,320.331L736.333,306.129Z" style="fill:rgb(255,210,91);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M634.713,746.712C634.716,746.748 634.716,746.784 634.713,746.82C634.514,749.088 622.586,751.061 607.91,749.775C593.235,748.488 581.802,745.673 582.001,743.404C582.011,743.284 582.054,743.168 582.128,743.056L586,723.628L585.814,704.823L588.102,681.262L639.681,685.722L632.567,705.529L634.792,746.717L634.713,746.712ZM1269.33,783.545C1269.34,783.578 1269.35,783.613 1269.35,783.649C1269.75,785.891 1258.74,790.884 1244.23,793.44C1229.72,795.996 1217.95,796.235 1217.55,793.992C1217.53,793.861 1217.55,793.724 1217.6,793.582L1217.5,776.628L1209.07,755.752C1207.7,752.371 1207.04,748.749 1207.11,745.104L1207.36,732.388L1258.34,723.347C1260.6,729.965 1261.99,735.725 1262.5,740.628C1263.01,745.531 1263.01,753.031 1262.5,763.128L1269.4,783.529L1269.33,783.545ZM1162.75,777.289L1162.75,777.397C1162.59,779.668 1150.7,781.849 1136,780.819C1121.31,779.789 1109.83,777.173 1109.99,774.902C1110,774.785 1110.03,774.672 1110.1,774.561L1113,756.628L1113.13,736.259L1115,712.662L1166.65,716.222L1159.5,751.128L1162.83,777.293L1162.75,777.289Z" style="fill:rgb(132,125,170);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M613.549,687.78C599.493,686.548 588.259,683.711 588.458,681.442C588.656,679.174 600.21,678.334 614.266,679.566C628.321,680.798 639.555,683.635 639.357,685.904C639.158,688.172 627.604,689.012 613.549,687.78ZM1233.63,732.099C1219.73,734.546 1208.15,734.713 1207.75,732.47C1207.36,730.228 1218.3,726.426 1232.2,723.978C1246.09,721.53 1257.68,721.364 1258.07,723.606C1258.47,725.849 1247.53,729.651 1233.63,732.099ZM1140.56,718.736C1126.48,717.749 1115.2,715.108 1115.36,712.836C1115.52,710.565 1127.06,709.523 1141.13,710.51C1155.21,711.496 1166.49,714.138 1166.33,716.409C1166.17,718.681 1154.63,719.722 1140.56,718.736Z" style="fill:rgb(96,88,142);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M625.864,723.461L625.337,729.477L619.648,728.98L619.253,733.492L611.937,732.851L612.332,728.34L592.255,726.583L592.781,720.567L614.361,705.146L621.677,705.787L620.174,722.964L625.864,723.461ZM600.624,722.51L612.534,723.552L613.457,712.997L600.624,722.51ZM1138,764.628C1127.15,764.628 1122,759.651 1122,750.59C1122,741.567 1127.15,736.628 1138,736.628C1148.81,736.628 1154,741.606 1154,750.59C1154,759.612 1148.81,764.628 1138,764.628ZM1138,759.628C1144.18,759.628 1147,756.416 1147,751.088C1147,745.801 1144.18,742.628 1138,742.628C1131.82,742.628 1129,745.76 1129,751.088C1129,756.456 1131.82,759.628 1138,759.628Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1.14514,-0.133145,0.0996166,0.85677,-589.428,-38.8963)">
        <g transform="matrix(48.8616,0,0,48.8616,1188,871)">
            <path d="M0.038,-0.19L0.17,-0.206C0.175,-0.172 0.186,-0.146 0.205,-0.128C0.223,-0.111 0.246,-0.102 0.272,-0.102C0.3,-0.102 0.324,-0.112 0.344,-0.134C0.363,-0.155 0.373,-0.184 0.373,-0.221C0.373,-0.255 0.363,-0.283 0.345,-0.303C0.326,-0.323 0.304,-0.333 0.277,-0.333C0.259,-0.333 0.238,-0.33 0.214,-0.323L0.229,-0.435C0.266,-0.434 0.294,-0.442 0.314,-0.459C0.333,-0.476 0.343,-0.499 0.343,-0.527C0.343,-0.551 0.336,-0.57 0.322,-0.584C0.307,-0.599 0.288,-0.606 0.265,-0.606C0.241,-0.606 0.221,-0.598 0.205,-0.582C0.188,-0.565 0.178,-0.542 0.174,-0.51L0.048,-0.532C0.057,-0.575 0.07,-0.61 0.088,-0.635C0.105,-0.661 0.13,-0.682 0.162,-0.697C0.194,-0.711 0.229,-0.719 0.269,-0.719C0.336,-0.719 0.39,-0.697 0.431,-0.654C0.464,-0.619 0.481,-0.579 0.481,-0.535C0.481,-0.472 0.447,-0.422 0.378,-0.385C0.419,-0.376 0.452,-0.356 0.476,-0.326C0.501,-0.295 0.513,-0.258 0.513,-0.215C0.513,-0.152 0.49,-0.098 0.444,-0.054C0.398,-0.01 0.341,0.012 0.273,0.012C0.208,0.012 0.154,-0.006 0.112,-0.044C0.069,-0.081 0.044,-0.13 0.038,-0.19Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M622.954,614.742L627.165,615.623L617.667,682.999C617.512,684.1 616.494,684.867 615.393,684.712C615.349,684.706 615.305,684.698 615.262,684.689C614.077,684.441 613.287,683.317 613.456,682.118L622.954,614.742ZM1147.34,645.632L1151.57,646.439L1143.24,713.971C1143.11,715.075 1142.1,715.859 1141,715.723C1140.96,715.718 1140.91,715.711 1140.87,715.702C1139.68,715.475 1138.87,714.365 1139.02,713.164L1147.34,645.632ZM1217.34,659.683L1221.61,659.145L1234.57,725.943C1234.78,727.035 1234.07,728.091 1232.97,728.303C1232.93,728.311 1232.89,728.318 1232.84,728.324C1231.64,728.475 1230.53,727.669 1230.3,726.481L1217.34,659.683Z" style="fill:rgb(43,37,78);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M530,334.129C531.918,334.129 533.402,333.553 534.5,332.129C535.3,331.092 536,329.409 536,328.129C536,326.516 535.707,324.767 534.5,323.629C533.434,322.624 531.554,322.129 530,322.129C528.417,322.129 526.572,322.592 525.5,323.629C524.327,324.763 524,326.542 524,328.129C524,331.166 526.686,334.129 530,334.129Z" style="fill:rgb(193,198,211);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M553,302.629C555.876,302.629 558.102,301.766 559.75,299.629C560.95,298.073 562,295.549 562,293.629C562,291.209 561.56,288.586 559.75,286.879C558.152,285.371 555.331,284.629 553,284.629C550.626,284.629 547.858,285.324 546.25,286.879C544.491,288.58 544,291.249 544,293.629C544,298.185 548.029,302.629 553,302.629Z" style="fill:rgb(193,198,211);"/>
    </g>
    <g transform="matrix(1,0,0,1,-368,-232)">
        <path d="M565.884,318.945C567.104,318.183 567.819,317.227 567.952,315.885C568.048,314.907 567.825,313.558 567.316,312.744C566.675,311.718 565.793,310.723 564.573,310.478C563.496,310.262 562.103,310.695 561.115,311.313C560.108,311.942 559.119,312.97 558.849,314.055C558.554,315.243 559.053,316.504 559.683,317.513C560.891,319.445 563.777,320.262 565.884,318.945Z" style="fill:rgb(193,198,211);"/>
    </g>
</svg>
