<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 903 887" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1250,1041.34C1252.36,1029.04 1254.73,1019.53 1257.09,1012.82C1259.46,1006.11 1264.03,995.947 1270.81,982.341C1276.16,995.094 1279.79,1004.47 1281.69,1010.47C1283.59,1016.48 1284.53,1021.56 1284.53,1025.71C1284.74,1021.74 1285.84,1017.44 1287.84,1012.82C1289.84,1008.19 1293.46,1001.55 1298.72,992.891C1304.53,1003.36 1308.63,1011.3 1311.01,1016.73C1313.4,1022.15 1316.39,1030.36 1320,1041.34L1250,1041.34ZM1085,1071.34C1086.96,1059.04 1088.92,1049.53 1090.88,1042.82C1092.84,1036.11 1096.63,1025.95 1102.24,1012.34C1106.68,1025.09 1109.68,1034.47 1111.26,1040.47C1112.83,1046.48 1113.61,1051.56 1113.61,1055.71C1113.78,1051.74 1114.69,1047.44 1116.35,1042.82C1118.01,1038.19 1121.01,1031.55 1125.36,1022.89C1130.18,1033.36 1133.58,1041.3 1135.55,1046.73C1137.53,1052.15 1140.01,1060.36 1143,1071.34L1085,1071.34Z" style="fill:rgb(255,148,106);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1024.74,1049.79L1024.74,997.192L1080.48,997.192L1080.48,1049.79" style="fill:rgb(244,229,173);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1137.89,1052.03L1137.89,1019.02L1210.91,1019.02L1210.91,1063.22" style="fill:rgb(244,229,173);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1210.35,1129.25L1210.35,1039.72L1311.8,1039.72L1311.8,1128.75" style="fill:rgb(244,229,173);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1019.17,1084.49L1019.17,1030.77L1030.31,1030.77L1030.31,1040.28L1037,1040.28L1037,1030.77L1048.71,1030.77L1048.71,1040.28L1055.4,1040.28L1055.4,1030.77L1067.1,1030.77L1067.1,1040.28L1073.79,1040.28L1073.79,1030.77L1087.73,1030.77L1087.73,1087.85" style="fill:rgb(175,162,110);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1302.88,1129.25L1302.88,997.192L1357.51,997.192L1357.51,1128.5" style="fill:rgb(218,202,139);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1008.02,1083.37L1008.02,1128.69L1102.77,1128.69L1102.77,1114.03C1102.77,1097.09 1116.5,1083.37 1133.43,1083.37C1150.36,1083.37 1164.09,1097.09 1164.09,1114.03L1164.09,1128.69L1215.37,1128.69L1215.37,1040.28L1200.88,1040.28L1198.65,1052.03L1188.61,1052.03L1188.61,1040.28L1169.11,1040.28L1169.11,1052.03L1159.07,1052.03L1159.07,1040.28L1140.12,1040.28L1140.12,1052.03L1130.64,1052.03L1130.64,1040.28L1111.69,1040.28L1111.69,1058.19L1093.3,1058.19L1076.02,1083.37L1008.02,1083.37Z" style="fill:rgb(218,202,139);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1003,1118.06L1107.79,1118.06L1107.79,1129.25L1003,1129.25L1003,1118.06ZM1161.3,1118.06L1220.39,1118.06L1220.39,1129.25L1161.3,1129.25L1161.3,1118.06Z" style="fill:rgb(175,162,110);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1037,1072.18L1037,1054.25C1037,1051.79 1039,1049.79 1041.46,1049.79C1043.92,1049.79 1045.92,1051.79 1045.92,1054.25L1045.92,1072.18L1037,1072.18ZM1244.91,1100.16L1244.91,1082.23C1244.91,1079.77 1246.91,1077.77 1249.37,1077.77C1251.83,1077.77 1253.83,1079.77 1253.83,1082.23L1253.83,1100.16L1244.91,1100.16ZM1060.97,1072.18L1060.97,1054.25C1060.97,1051.79 1062.97,1049.79 1065.43,1049.79C1067.89,1049.79 1069.89,1051.79 1069.89,1054.25L1069.89,1072.18L1060.97,1072.18ZM1268.88,1100.16L1268.88,1082.23C1268.88,1079.77 1270.88,1077.77 1273.34,1077.77C1275.8,1077.77 1277.8,1079.77 1277.8,1082.23L1277.8,1100.16L1268.88,1100.16ZM1326.29,1049.79L1326.29,1031.87C1326.29,1029.41 1328.29,1027.41 1330.75,1027.41C1333.22,1027.41 1335.21,1029.41 1335.21,1031.87L1335.21,1049.79L1326.29,1049.79ZM1326.29,1099.04L1326.29,1081.11C1326.29,1078.65 1328.29,1076.65 1330.75,1076.65C1333.22,1076.65 1335.21,1078.65 1335.21,1081.11L1335.21,1099.04L1326.29,1099.04ZM1184.71,1118.06L1184.71,1100.13C1184.71,1095.82 1188.21,1092.32 1192.52,1092.32C1196.83,1092.32 1200.32,1095.82 1200.32,1100.13L1200.32,1118.06L1184.71,1118.06Z" style="fill:rgb(51,78,98);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1159.23,953.362L1176.35,914.933L1198.58,964.827C1200.29,967.867 1201.99,970.821 1203.66,973.69C1209.77,984.119 1219.06,999.228 1231.53,1019.02C1207.41,1021.9 1188.74,1023.34 1175.5,1023.34C1162.26,1023.34 1143.41,1021.9 1118.94,1019.02C1132.72,998.908 1142.57,983.799 1148.48,973.69C1152.12,967.465 1155.71,960.689 1159.23,953.362ZM1011.92,997.192C1021.73,987.724 1028.42,978.025 1031.98,968.094C1035.55,958.163 1042.61,939.883 1053.17,913.254C1063.69,939.401 1070.75,957.68 1074.35,968.094C1077.95,978.507 1084.45,988.207 1093.86,997.192C1080.85,999.431 1067.1,1000.55 1052.61,1000.55C1038.12,1000.55 1024.55,999.431 1011.92,997.192ZM1289.5,999.431C1299.32,989.963 1306,980.263 1309.57,970.332C1313.14,960.401 1320.2,942.121 1330.75,915.493C1341.27,941.639 1348.33,959.919 1351.93,970.332C1355.53,980.745 1362.03,990.445 1371.44,999.431C1358.44,1001.67 1344.69,1002.79 1330.19,1002.79C1315.7,1002.79 1302.14,1001.67 1289.5,999.431Z" style="fill:rgb(51,78,98);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1115.4,624.081C1128.55,672.07 1127.18,710.98 1111.3,740.81C1095.42,770.64 1089.39,808.265 1093.23,853.686L1132.86,803.262C1132.28,832.231 1132.64,854.345 1133.94,869.603C1135.24,884.86 1138.04,899.892 1142.35,914.7L1161.62,872.096C1170.01,890.759 1179.19,906.325 1189.15,918.795C1199.12,931.265 1215.29,947.425 1237.66,967.274C1241.86,936.992 1243.88,914.001 1243.72,898.3C1243.57,882.599 1241.17,867.227 1236.53,852.185L1263.77,880.961C1265.58,866.254 1263.76,852.805 1258.31,840.615C1252.87,828.424 1245.06,811.673 1234.88,790.361L1277.8,823.227C1264.72,782.909 1240.9,749.192 1206.36,722.076C1171.82,694.96 1150.7,662.875 1142.98,625.82C1138.35,623.716 1134.24,622.52 1130.62,622.233C1127,621.946 1121.93,622.562 1115.4,624.081Z" style="fill:rgb(255,148,106);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1127.34,624.192C1133.21,650.605 1137.65,672.52 1140.68,689.937C1142.72,701.68 1144.69,718.726 1146.6,741.073C1148.62,764.636 1158.92,786.723 1175.68,803.41L1190.13,817.803L1191.97,810.613C1198.4,785.501 1194.83,758.874 1182,736.346C1171.09,717.166 1162.77,701.512 1157.07,689.385C1148.25,670.652 1140.26,648.341 1133.11,622.451L1127.34,624.192Z" style="fill:rgb(255,230,118);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M960,899.998C955.5,911.118 951.235,919.031 947.207,923.738C945.09,926.211 942.39,929.11 939.106,932.435C938.182,933.371 936.673,933.381 935.737,932.456C935,931.728 934.818,930.607 935.288,929.683L940.533,919.361C941.11,918.224 940.657,916.835 939.52,916.258C939.392,916.193 939.258,916.14 939.121,916.099C937.545,915.64 935.858,916.3 935.012,917.707L923.697,936.529C923.127,937.476 921.899,937.782 920.952,937.213C920.283,936.811 919.909,936.056 919.994,935.281L922.314,914.142L938.302,892.341L960,899.998Z" style="fill:rgb(67,123,166);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M760,888.001L674.858,783.341L523.289,830.329C521.707,830.82 520.821,832.5 521.312,834.083C521.67,835.239 522.688,836.066 523.892,836.181L598.622,843.318L618.086,884.466C618.765,885.901 620.444,886.559 621.917,885.966L666.294,868.124L698.966,893.519C699.489,893.926 700.132,894.148 700.795,894.151L747.657,894.341L760,888.001Z" style="fill:rgb(51,78,98);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M861.31,872.839L894.615,921.902L905.941,894.731C907.613,890.722 910.261,887.196 913.646,884.474C924.836,875.475 941.202,877.251 950.201,888.441L960,900.625L950.888,900.498C944.835,900.414 939.194,903.554 936.077,908.744L902.688,964.341L862.923,935.656L865.71,1044.25L895.645,1074.08L925.742,1081.2C927.095,1081.52 928.255,1082.39 928.941,1083.6C930.302,1086 929.458,1089.05 927.055,1090.41L882.456,1115.68C882.447,1115.69 882.438,1115.69 882.428,1115.7C870.407,1122.49 855.156,1118.25 848.365,1106.23L816.503,1049.83L783.512,1049.83L783.512,1109.59L803.093,1120.53C804.358,1121.24 805.142,1122.58 805.142,1124.03C805.142,1126.23 803.351,1128.03 801.142,1128.03L732.819,1128.03C732.093,1128.03 731.367,1127.98 730.646,1127.89C720.778,1126.69 713.751,1117.72 714.95,1107.85L722.004,1049.83L617.034,1105.73C611.953,1108.44 606.428,1110.21 600.722,1110.96L469,1128.25L675.034,973.872L724.11,863.265C724.871,861.549 725.81,859.95 726.901,858.484L725.099,858.486L743.515,422.983C745.739,370.404 788.332,328.546 840.942,327.239L1118.5,320.341C1171.5,318.674 1198,349.008 1198,411.341C1198,420.712 1187.17,470.712 1165.5,561.341C1179.13,573.404 1180.4,594.231 1168.34,607.86C1167.57,608.722 1166.77,609.543 1165.92,610.32C1165.85,610.38 1165.79,610.44 1165.72,610.501C1163.56,612.534 1161.22,614.315 1158.75,615.834L1159.67,621.146C1160.02,623.196 1159.87,625.3 1159.24,627.282C1157.07,634.125 1149.76,637.912 1142.92,635.739L1127.65,630.892L1115.7,637.903C1114.43,638.648 1113.04,639.172 1111.6,639.452C1104.55,640.819 1097.73,636.213 1096.36,629.164L1092.73,610.413L1092.5,610.341L1070,561.341L985,493.341L915.659,489.209C906.033,488.636 897.666,495.749 896.682,505.342L860.849,854.878L861.31,872.839Z" style="fill:rgb(122,198,255);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1171.12,507.335C1176.28,503.229 1188.61,506.735 1187.74,516.546C1186.88,526.357 1179.95,541.953 1168.28,532.057C1156.62,522.161 1165.97,511.44 1171.12,507.335ZM1076.06,511.051C1078.58,504.962 1091.06,502.051 1095.06,511.051C1099.06,520.051 1100.56,537.051 1085.56,534.051C1070.56,531.051 1073.55,517.139 1076.06,511.051Z" style="fill:white;"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1167.06,522.142C1167.08,520.761 1168.22,519.662 1169.6,519.686L1180.6,519.878C1181.98,519.902 1183.08,521.041 1183.05,522.421C1183.03,523.802 1181.89,524.901 1180.51,524.877L1169.51,524.685C1168.13,524.661 1167.03,523.522 1167.06,522.142ZM1079.5,522.201C1079.53,520.821 1080.66,519.721 1082.04,519.745L1093.04,519.937C1094.42,519.961 1095.52,521.1 1095.5,522.48C1095.47,523.861 1094.34,524.961 1092.96,524.936L1081.96,524.744C1080.58,524.72 1079.48,523.582 1079.5,522.201Z" style="fill:rgb(51,78,98);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M721.651,1049.25L706,1133.34L801.451,1132.84C805.869,1132.82 809.432,1129.22 809.409,1124.8C809.402,1123.45 809.052,1122.12 808.394,1120.94L807.174,1118.76L787.051,1109.23L783.697,1049.81L721.651,1049.25Z" style="fill:rgb(67,123,166);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M694.651,764.355L562.698,785.993C561.063,786.261 559.955,787.804 560.223,789.439C560.418,790.631 561.31,791.589 562.485,791.871L647.848,812.308L654.044,854.359C654.276,855.934 655.699,857.053 657.284,856.909L720.311,851.168L734.57,881.336C735.009,882.263 735.891,882.902 736.908,883.03L795.492,890.396L805.591,901.381C807.157,903.085 809.73,903.394 811.655,902.11C813.558,900.84 814.198,898.342 813.141,896.314L808.713,887.819C808.445,887.306 808.183,886.79 807.926,886.272L765.524,800.719C752.452,774.346 723.698,759.592 694.651,764.355Z" style="fill:rgb(67,123,166);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M731.849,714.093L670.195,757.888C668.845,758.848 666.972,758.531 666.012,757.18C665.381,756.291 665.281,755.131 665.752,754.148L737.6,604.021L681.172,631.185C679.679,631.904 677.887,631.276 677.168,629.783C676.786,628.99 676.772,628.069 677.129,627.265L741.008,483.368L702.878,503.569C701.414,504.345 699.599,503.786 698.823,502.322C698.38,501.486 698.357,500.489 698.763,499.633L749.248,393.085L731.849,714.093ZM953.043,323.639L898.72,383.97C896.872,386.022 893.71,386.188 891.658,384.34C890.169,383 889.625,380.9 890.274,379.005L905.554,334.385L813.496,401.684C811.266,403.313 808.138,402.827 806.508,400.598C805.438,399.134 805.244,397.205 806.003,395.557L837.109,327.956L953.043,323.639Z" style="fill:rgb(51,78,98);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M675,974.341L631.088,983.079C629.463,983.402 628.408,984.982 628.731,986.607C628.795,986.925 628.909,987.23 629.07,987.512L635.333,998.454L597.876,1008.26C596.273,1008.68 595.314,1010.32 595.733,1011.92C595.879,1012.48 596.183,1012.99 596.609,1013.37L604.773,1020.86L565.133,1030.96C563.528,1031.37 562.558,1033 562.967,1034.61C563.207,1035.55 563.889,1036.32 564.796,1036.67L577.547,1041.58L534.514,1058.22C532.968,1058.81 532.2,1060.55 532.797,1062.1C533.165,1063.05 533.991,1063.75 534.99,1063.95L551.432,1067.34L675,974.341Z" style="fill:rgb(51,78,98);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1107.79,538.942C1107.67,544.904 1077.38,511.174 1039.24,426.902C1025.61,396.777 1003.42,336.81 972.665,247L1030.81,344.17L1042.64,278.021L1064.83,401.598L1082.47,372.01C1099.54,474.139 1107.98,529.783 1107.79,538.942ZM1164.86,534.07C1162.15,537.806 1157.94,502.88 1171.1,432.335C1175.81,407.117 1188.29,359.119 1208.56,288.341L1202.32,375.99L1239,339.208L1198.42,427.64L1222.61,416.684C1188.27,489.202 1169.02,528.331 1164.86,534.07Z" style="fill:rgb(51,78,98);"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1165.93,561.377C1165.73,561.89 1165.14,562.141 1164.63,561.937C1164.12,561.733 1163.87,561.152 1164.07,560.639C1173.29,537.418 1189.77,531.522 1212.95,543.113C1219.39,546.334 1226.06,552.456 1233.93,561.887C1237.11,565.706 1240.46,570.004 1244.59,575.541C1246.8,578.509 1254.49,588.977 1255.54,590.397C1270.31,610.334 1280.1,621.583 1290.97,629.623C1304.77,639.83 1318.12,642.305 1332.09,635.931C1337.2,633.597 1341.82,631.293 1345.93,629.021C1360.75,620.838 1366.13,602.193 1357.95,587.376C1356.39,584.565 1354.41,582.016 1352.07,579.821C1344.82,573.034 1333.44,573.407 1326.65,580.654C1326.13,581.214 1325.64,581.807 1325.19,582.43L1322.31,586.426C1321.99,586.874 1321.36,586.975 1320.92,586.652C1320.47,586.329 1320.37,585.704 1320.69,585.256L1323.57,581.26C1324.07,580.569 1324.61,579.91 1325.19,579.287C1332.74,571.233 1345.38,570.819 1353.43,578.361C1355.93,580.699 1358.04,583.415 1359.7,586.409C1368.41,602.193 1362.68,622.055 1346.9,630.771C1342.74,633.07 1338.08,635.396 1332.92,637.751C1318.23,644.454 1304.13,641.84 1289.78,631.231C1278.7,623.037 1268.82,611.678 1253.94,591.587C1252.88,590.162 1245.19,579.698 1242.98,576.737C1238.87,571.226 1235.55,566.955 1232.39,563.169C1224.68,553.932 1218.19,547.972 1212.05,544.902C1189.9,533.827 1174.71,539.264 1165.93,561.377ZM1096.57,564.377C1087.79,542.264 1072.6,536.827 1050.45,547.902C1042.27,551.989 1034.21,561.761 1023.68,578.973C1021.95,581.805 1020.13,584.848 1017.98,588.543C1017.13,589.991 1016.26,591.482 1015.11,593.47C1015.23,593.265 1012.88,597.314 1012.23,598.44C998.449,622.177 990.654,633.696 981.203,642.287C968.709,653.644 956.207,655.331 942.913,645.65C920.336,629.208 916.312,610.634 931.019,590.384C937.877,580.942 951.089,578.847 960.53,585.705C963.915,588.163 966.494,591.57 967.94,595.496C968.131,596.014 967.865,596.589 967.347,596.78C966.829,596.97 966.254,596.705 966.063,596.187C964.754,592.633 962.419,589.548 959.355,587.323C950.807,581.115 938.846,583.011 932.638,591.559C918.61,610.873 922.367,628.213 944.09,644.033C956.559,653.113 968.022,651.565 979.858,640.807C989.1,632.406 996.828,620.985 1010.5,597.436C1011.15,596.311 1013.5,592.262 1013.38,592.467C1014.53,590.477 1015.4,588.984 1016.25,587.534C1018.41,583.828 1020.23,580.774 1021.97,577.929C1032.7,560.403 1040.93,550.425 1049.55,546.113C1072.74,534.522 1089.21,540.418 1098.43,563.639C1098.63,564.152 1098.38,564.733 1097.87,564.937C1097.36,565.141 1096.78,564.89 1096.57,564.377Z" style="fill:rgb(51,78,98);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-469,-247)">
        <path d="M1097.5,563.841C1112.07,565.287 1121.91,569.787 1127,577.341C1132.09,584.895 1130.43,595.228 1122,608.341L1097.5,563.841Z" style="fill:rgb(67,123,166);"/>
    </g>
</svg>
