<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 1102 890" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;">
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M1370,888L1404.31,789.104C1414.49,759.753 1406.77,727.16 1384.5,705.5L1383.53,704.559C1361.14,682.784 1350.21,651.798 1353.99,620.795L1366.86,515.023C1373.53,460.199 1334.49,410.348 1279.67,403.677C1276.87,403.337 1274.07,403.116 1271.25,403.012L1230,401.5L1145.82,321.143C1117.13,293.761 1074.34,287.327 1038.87,305.063L943.548,352.725C928.112,360.443 910.941,364.035 893.706,363.151L816.331,359.183C775.05,357.067 738.439,385.484 730.25,426L717.28,490.166C716.427,494.384 715.848,498.653 715.545,502.946L703.382,675.484C702.799,683.754 700.663,691.84 697.086,699.319L663.637,769.257C653.231,791.016 651.039,815.803 657.466,839.05L671,888L1370,888Z" style="fill:rgb(196,196,222);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M671,888L581.288,985.381C554.72,1014.22 556.561,1059.14 585.401,1085.71C597.694,1097.03 613.589,1103.66 630.287,1104.41L1295.78,1134.63C1334.95,1136.41 1368.15,1106.1 1369.93,1066.92C1369.98,1065.85 1370,1064.78 1370,1063.7L1370,888L671,888Z" style="fill:rgb(139,139,181);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M833.5,567L833.5,245L806.5,245L806.5,580.5C806.5,587.956 812.544,594 820,594L1040.22,594C1055.94,594 1071.37,598.285 1084.84,606.394L1135.24,636.738C1152.91,647.378 1173.15,653 1193.78,653L1455.5,653L1455.5,626L1193.78,626C1178.06,626 1162.63,621.715 1149.16,613.606L1098.76,583.262C1081.09,572.622 1060.85,567 1040.22,567L833.5,567Z" style="fill:rgb(255,132,132);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M1337,618L1481,618L1481,667L1337,667L1337,618ZM776,559L920,559L920,608L776,608L776,559ZM798.5,442.5L798.5,320L859,320L847.5,442.5L798.5,442.5ZM1085.5,549L1201.5,604L1201.5,653C1198.17,665.726 1184.5,671.06 1160.5,669C1136.5,666.94 1099.66,657.274 1050,640L1085.5,549Z" style="fill:rgb(222,98,98);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M974,654L642,335.5L613,746L967.5,683L974,654Z" style="fill:rgb(255,238,205);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M611.036,746.127C681.133,739.965 729.435,637.087 718.922,516.343C708.408,395.599 643.061,302.711 572.964,308.873C502.867,315.035 454.565,417.913 465.078,538.657C475.592,659.401 540.939,752.289 611.036,746.127Z" style="fill:rgb(245,203,118);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M729,669.5L729,687.5L621,687.5L621,669.5L570,669.5L570,649.5L782,649.5L782,669.5L729,669.5ZM727,669.5L623,669.5L623,685.5L727,685.5L727,669.5ZM572,667.5L675,667.5L675,651.5L572,651.5L572,667.5ZM677,667.5L780,667.5L780,651.5L677,651.5L677,667.5ZM647,384.5L544,384.5L544,400.5L647,400.5L647,384.5ZM597,382.5L649,382.5L649,402.5L436,402.5L436,382.5L490,382.5L490,364.5L597,364.5L597,382.5ZM542,400.5L542,384.5L438,384.5L438,400.5L542,400.5ZM595,382.5L595,366.5L492,366.5L492,382.5L595,382.5ZM383,429.5L595,429.5L595,449.5L383,449.5L383,429.5ZM488,447.5L488,431.5L385,431.5L385,447.5L488,447.5ZM490,447.5L593,447.5L593,431.5L490,431.5L490,447.5Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <rect x="676" y="715.5" width="105" height="18" style="fill:none;fill-rule:nonzero;stroke:white;stroke-width:2px;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <rect x="729" y="669.5" width="105" height="18" style="fill:none;fill-rule:nonzero;stroke:white;stroke-width:2px;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M954.966,695C937.678,723.847 937.678,740.565 954.966,745.155C972.253,749.744 986.931,735.648 999,702.867L954.966,695Z" style="fill:rgb(53,53,53);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M975.5,1048C1092.86,1048 1188,1036.36 1188,1022C1188,1007.64 1092.86,996 975.5,996C858.139,996 763,1007.64 763,1022C763,1036.36 858.139,1048 975.5,1048Z" style="fill:rgb(53,53,53);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M1232.39,1009.78L1255.35,1026.01C1255.51,1026.12 1255.67,1026.24 1255.82,1026.37C1258.46,1028.63 1258.76,1032.61 1256.5,1035.25C1253.79,1038.42 1249.39,1039.55 1245.48,1038.08C1219.5,1028.35 1204.51,1021.32 1200.5,1017L1139,963.5L1061,944L1056,1046C1049.66,1046.56 1044.83,1046.89 1041.5,1047C1011.5,1048 987.169,1048.37 971.222,1048.61C955.228,1048.86 936.431,1048.61 909.186,1047.78C906.648,1047.7 901.478,1047.14 893.677,1046.1L904.156,936.741L839.724,1002.16C834.59,1007.37 828.9,1012 822.756,1015.97L764.243,1053.79C757.537,1058.12 750.08,1061.16 742.257,1062.75L623,1087C621.254,1072.79 627.087,1059.95 640.5,1048.5C652.206,1038.51 691.198,1031.73 719,1030C721.657,1029.83 744.562,1011 787.713,973.489C789.234,972.167 790.661,970.743 791.987,969.225L865.649,884.888C883.422,864.538 908.809,852.429 935.809,851.423L974,850L982.539,811.46L961.741,811.46C946.277,811.46 933.741,798.924 933.741,783.46C933.741,780.383 934.249,777.327 935.242,774.416L955.972,713.681C965.092,686.96 990.199,669 1018.43,669L1032.42,669C1056.16,669 1075.41,688.246 1075.41,711.986C1075.41,716.958 1074.54,721.892 1072.86,726.569L1026,856.5L1152.75,902.891C1169.92,909.177 1185.04,920.05 1196.46,934.329L1231,977.5C1261,976.167 1277.33,977.833 1280,982.5C1282.21,986.375 1290.06,994.963 1303.54,1008.26C1306.22,1010.9 1306.24,1015.21 1303.6,1017.89C1302.69,1018.82 1301.52,1019.46 1300.25,1019.75C1292.51,1021.49 1284.4,1019.6 1278.23,1014.62L1272.5,1010C1251.97,1011.12 1238.97,1011.12 1233.5,1010C1233.13,1009.92 1232.76,1009.85 1232.39,1009.78Z" style="fill:rgb(255,205,205);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M951,848.5C960,863.833 975,872.5 996,874.5C1017,876.5 1032.33,871.5 1042,859.5L1148,880.906L1103.5,962.5L1081,959.5L1061.31,1047.48L877.054,1060L904.693,938.099L877.054,989.865L800,904.284C833.289,871.557 862.743,851.797 888.361,845.003C913.979,838.21 934.859,839.375 951,848.5Z" style="fill:rgb(255,249,214);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M1060.69,720.384L1081,725.244C1081.18,759.746 1072.12,774.736 1053.82,770.214C1044.67,767.953 1039.49,762.388 1038.28,753.519C1030.59,759.794 1022.59,761.513 1014.27,758.673C997.399,752.915 996.615,736.024 1011.92,708L1061,719.703C1060.9,719.931 1060.79,720.158 1060.69,720.384Z" style="fill:rgb(53,53,53);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M947,692.967L1090,729C1084.07,723.413 1081.24,719.149 1081.5,716.207C1084.45,682.855 1065.94,672.317 1027.96,664.071C989.796,655.785 978.15,654.851 961.879,683.145C960.503,685.538 955.544,688.812 947,692.967Z" style="fill:rgb(60,60,160);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M976,682L1084.5,711L1087.5,699.5L980,667L976,682Z" style="fill:rgb(38,38,111);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M970.5,688C981.27,688 990,679.27 990,668.5C990,657.73 981.27,649 970.5,649C959.73,649 951,657.73 951,668.5C951,679.27 959.73,688 970.5,688Z" style="fill:rgb(245,203,118);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M970.5,683C979.06,683 986,676.508 986,668.5C986,660.492 979.06,654 970.5,654C961.94,654 955,660.492 955,668.5C955,676.508 961.94,683 970.5,683Z" style="fill:rgb(255,238,205);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M964.331,718.876C962.213,713.869 959.145,711.5 955,711.5L955,708.5C960.174,708.5 964.105,711.342 966.685,716.794C976.72,710.814 985.147,712.996 991.287,723.228C991.713,723.939 991.483,724.86 990.772,725.286C990.062,725.712 989.14,725.482 988.714,724.772C983.217,715.61 976.272,714.081 967.116,720.061L946.373,755.157L958.534,757.528C959.347,757.686 959.877,758.474 959.719,759.287C959.56,760.1 958.773,760.631 957.96,760.472L943.713,757.695C942.696,757.497 942.182,756.352 942.709,755.46L964.331,718.876ZM976,733C973.791,733 972,731.209 972,729C972,726.791 973.791,725 976,725C978.21,725 980,726.791 980,729C980,731.209 978.21,733 976,733ZM955,727C952.791,727 951,725.209 951,723C951,720.791 952.791,719 955,719C957.21,719 959,720.791 959,723C959,725.209 957.21,727 955,727ZM948.641,777.666C944.089,776.424 941.232,772.656 942.259,769.251C943.286,765.846 947.808,764.092 952.36,765.334C956.911,766.576 959.769,770.344 958.742,773.749C957.715,777.154 953.193,778.908 948.641,777.666ZM937.093,790.981C937.379,790.204 938.242,789.806 939.019,790.092C939.796,790.379 940.194,791.241 939.908,792.019L936.408,801.519C936.122,802.296 935.259,802.694 934.482,802.408C933.705,802.121 933.306,801.259 933.593,800.481L937.093,790.981ZM943.093,792.981C943.379,792.204 944.242,791.806 945.019,792.092C945.796,792.379 946.194,793.241 945.908,794.019L942.408,803.519C942.122,804.296 941.259,804.694 940.482,804.408C939.705,804.121 939.306,803.259 939.593,802.481L943.093,792.981ZM949.093,794.981C949.379,794.204 950.242,793.806 951.019,794.092C951.796,794.379 952.194,795.241 951.908,796.019L948.408,805.519C948.122,806.296 947.259,806.694 946.482,806.408C945.705,806.121 945.306,805.259 945.593,804.481L949.093,794.981Z" style="fill:rgb(53,53,53);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M1115,400.5L1115,420.5L902,420.5L902,400.5L922,400.5L922,380.5L1134,380.5L1134,400.5L1115,400.5ZM1027,441.5L1134,441.5L1134,461.5L1027,461.5L1027,441.5ZM784,805.5L784,824.5L676,824.5L676,805.5L623,805.5L623,785.5L835,785.5L835,805.5L784,805.5ZM1356,551.5L1410,551.5L1410,571.5L1197,571.5L1197,551.5L1144,551.5L1144,531.5L1356,531.5L1356,551.5ZM1277,778L1348,778L1348,798L1241,798L1241,779L1170,779L1170,759L1277,759L1277,778Z" style="fill:rgb(139,139,181);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <rect x="384" y="365.5" width="105" height="18" style="fill:none;fill-rule:nonzero;stroke:white;stroke-width:2px;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M559.468,546.528L559.468,555.952L550.788,555.952L550.788,563.02L539.628,563.02L539.628,555.952L509,555.952L509,546.528L539.628,519.62L550.788,519.62L550.788,546.528L559.468,546.528ZM521.028,546.528L539.628,546.528L539.628,530.78L521.028,546.528ZM587.802,563.64C570.566,563.64 562.382,555.704 562.382,541.258C562.382,526.874 570.566,519 587.802,519C604.976,519 613.222,526.936 613.222,541.258C613.222,555.642 604.976,563.64 587.802,563.64ZM587.802,554.278C597.598,554.278 602.062,549.38 602.062,541.258C602.062,533.198 597.598,528.362 587.802,528.362C578.006,528.362 573.542,533.136 573.542,541.258C573.542,549.442 578.006,554.278 587.802,554.278ZM667.348,546.528L667.348,555.952L658.668,555.952L658.668,563.02L647.508,563.02L647.508,555.952L616.88,555.952L616.88,546.528L647.508,519.62L658.668,519.62L658.668,546.528L667.348,546.528ZM628.908,546.528L647.508,546.528L647.508,530.78L628.908,546.528Z" style="fill:white;fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M1026,582L1035,582L1035,602L1026,602L1026,582ZM1252,640L1261,640L1261,660L1252,660L1252,640Z" style="fill:rgb(139,139,181);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M1124,1030L962,1030L962,1048.5L947,1048.5L947,986.5C947,982.358 950.358,979 954.5,979C958.642,979 962,982.358 962,986.5L962,1015L1124,1015L1124,986.5C1124,982.358 1127.36,979 1131.5,979C1135.64,979 1139,982.358 1139,986.5L1139,1038.5L1124,1040.5L1124,1030Z" style="fill:rgb(255,132,132);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M670.126,1063.05C663.522,1065.03 659,1071.11 659,1078C659,1078.83 659.672,1079.5 660.5,1079.5C661.328,1079.5 662,1078.83 662,1078C662,1072.43 665.653,1067.52 670.988,1065.92L685.931,1061.44C686.38,1061.3 686.74,1060.97 686.904,1060.53C687.499,1058.94 691.655,1056.22 699.116,1052.87C706.036,1049.75 709.911,1049.24 714.574,1049.83L715.21,1049.92C715.459,1049.95 715.65,1049.97 715.834,1049.99C716.658,1050.08 717.399,1049.49 717.491,1048.67C717.582,1047.84 716.989,1047.1 716.166,1047.01C716.002,1046.99 715.826,1046.97 715.593,1046.94C715.521,1046.93 715.086,1046.87 714.954,1046.86C709.757,1046.19 705.324,1046.78 697.884,1050.13C690.349,1053.52 685.935,1056.27 684.449,1058.75L670.126,1063.05Z" style="fill:rgb(53,53,53);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M1031,767.659C1051.28,772.925 1063.07,768.992 1066.38,755.859C1068.9,745.9 1063.64,737.683 1052.11,740.469C1049.66,741.061 1044.95,745.571 1038,754L1031,767.659Z" style="fill:rgb(255,205,205);"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M1248.71,994.486L1263.95,992.346C1271.97,991.221 1280.08,993.557 1286.27,998.77L1304.53,1014.15C1305.17,1014.68 1306.11,1014.6 1306.65,1013.97C1307.18,1013.33 1307.1,1012.39 1306.47,1011.85L1288.2,996.475C1281.36,990.713 1272.39,988.132 1263.54,989.375L1248.29,991.515C1247.47,991.63 1246.9,992.388 1247.01,993.209C1247.13,994.029 1247.89,994.601 1248.71,994.486Z" style="fill:rgb(53,53,53);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-379.877,-245)">
        <path d="M1009,401.5L922,401.5L922,399.5L1028,399.5L1028,380.5L1030,380.5L1030,399.5L1115,399.5L1115,401.5L1011,401.5L1011,420.5L1009,420.5L1009,401.5ZM1304,552.5L1197.5,552.5C1196.95,552.5 1196.5,552.052 1196.5,551.5C1196.5,550.948 1196.95,550.5 1197.5,550.5L1250,550.5L1250,531.5L1252,531.5L1252,550.5L1356,550.5C1356.55,550.5 1357,550.948 1357,551.5C1357,552.052 1356.55,552.5 1356,552.5L1306,552.5L1306,571.5L1304,571.5L1304,552.5ZM1241,779.5C1240.45,779.5 1240,779.052 1240,778.5C1240,777.948 1240.45,777.5 1241,777.5L1277,777.5C1277.55,777.5 1278,777.948 1278,778.5C1278,779.052 1277.55,779.5 1277,779.5L1241,779.5ZM728,804.5L728,785.5L730,785.5L730,804.5L784,804.5L784,806.5L677,806.5L677,804.5L728,804.5Z" style="fill:rgb(53,53,53);fill-rule:nonzero;"/>
    </g>
</svg>
